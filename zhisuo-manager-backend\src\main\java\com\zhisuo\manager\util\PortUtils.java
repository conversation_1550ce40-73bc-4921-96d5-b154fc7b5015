package com.zhisuo.manager.util;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.ServerSocket;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 端口工具类
 * 用于检查端口占用、获取占用进程PID、关闭进程等功能
 */
@Slf4j
public class PortUtils {

    /**
     * 检查端口是否被占用
     *
     * @param port 端口号
     * @return true-被占用，false-未被占用
     */
    public static boolean isPortInUse(int port) {
        try (ServerSocket serverSocket = new ServerSocket(port)) {
            return false;
        } catch (IOException e) {
            return true;
        }
    }

    /**
     * 获取占用指定端口的进程PID列表
     *
     * @param port 端口号
     * @return 进程PID列表
     */
    public static List<String> getProcessIdsByPort(int port) {
        List<String> pids = new ArrayList<>();
        String os = System.getProperty("os.name").toLowerCase();

        try {
            Process process;
            if (os.contains("win")) {
                // Windows系统 - 使用cmd执行管道命令
                String command = "cmd /c netstat -ano | findstr :" + port;
                log.info("执行命令: {}", command);
                process = Runtime.getRuntime().exec(command);
            } else {
                // Linux/Unix系统
                String command = "lsof -ti:" + port;
                log.info("执行命令: {}", command);
                process = Runtime.getRuntime().exec(command);
            }
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

            String line;
            while ((line = reader.readLine()) != null) {
                log.info("读取到netstat输出行: {}", line);
                if (os.contains("win")) {
                    // Windows: 解析netstat输出
                    String pid = parseWindowsNetstatOutput(line, port);
                    if (pid != null && !pids.contains(pid)) {
                        log.info("找到PID: {}", pid);
                        pids.add(pid);
                    }
                } else {
                    // Linux/Unix: lsof直接返回PID
                    line = line.trim();
                    if (!line.isEmpty() && !pids.contains(line)) {
                        pids.add(line);
                    }
                }
            }

            int exitCode = process.waitFor();
            log.info("命令执行完成，退出码: {}", exitCode);
            reader.close();

            log.info("最终获取到的PID列表: {}", pids);
        } catch (Exception e) {
            log.error("获取端口{}占用进程失败: {}", port, e.getMessage(), e);
        }

        return pids;
    }

    /**
     * 解析Windows netstat命令输出，提取PID
     *
     * @param line netstat输出行
     * @param port 目标端口
     * @return PID或null
     */
    private static String parseWindowsNetstatOutput(String line, int port) {
        try {
            // netstat输出格式: TCP    0.0.0.0:8081    0.0.0.0:0    LISTENING    12345
            // 或者: TCP    [::]:8081    [::]:0    LISTENING    12345
            log.info("解析netstat行: {}", line);

            String[] parts = line.trim().split("\\s+");
            log.info("分割后的部分: {}", java.util.Arrays.toString(parts));

            if (parts.length >= 5) {
                String protocol = parts[0];
                String localAddress = parts[1];
                String remoteAddress = parts[2];
                String state = parts[3];
                String pid = parts[4];

                log.info("协议: {}, 本地地址: {}, 远程地址: {}, 状态: {}, PID: {}",
                         protocol, localAddress, remoteAddress, state, pid);

                // 检查是否是TCP协议、LISTENING状态且端口匹配
                if ("TCP".equals(protocol) && "LISTENING".equals(state)) {
                    // 检查端口是否匹配
                    if (localAddress.endsWith(":" + port) ||
                        localAddress.contains("]:" + port)) {
                        log.info("找到匹配的进程 PID: {}", pid);
                        return pid;
                    } else {
                        log.info("端口不匹配，期望: {}, 实际: {}", port, localAddress);
                    }
                } else {
                    log.info("协议或状态不匹配，协议: {}, 状态: {}", protocol, state);
                }
            } else {
                log.info("分割后的部分数量不足: {}", parts.length);
            }
        } catch (Exception e) {
            log.error("解析netstat输出失败: {}, 错误: {}", line, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 根据PID关闭进程
     *
     * @param pid 进程ID
     * @return true-成功，false-失败
     */
    public static boolean killProcess(String pid) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            String command;
            
            if (os.contains("win")) {
                // Windows系统
                command = "taskkill /PID " + pid + " /F";
            } else {
                // Linux/Unix系统
                command = "kill -9 " + pid;
            }
            
            log.info("执行关闭进程命令: {}", command);
            Process process = Runtime.getRuntime().exec(command);
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                log.info("成功关闭进程 PID: {}", pid);
                return true;
            } else {
                log.warn("关闭进程失败，退出码: {}, PID: {}", exitCode, pid);
                return false;
            }
        } catch (Exception e) {
            log.error("关闭进程失败 PID: {}, 错误: {}", pid, e.getMessage());
            return false;
        }
    }

    /**
     * 关闭占用指定端口的所有进程
     *
     * @param port 端口号
     * @return 成功关闭的进程数量
     */
    public static int killProcessesByPort(int port) {
        log.info("开始检查端口 {} 的占用情况", port);
        
        if (!isPortInUse(port)) {
            log.info("端口 {} 未被占用", port);
            return 0;
        }
        
        List<String> pids = getProcessIdsByPort(port);
        if (pids.isEmpty()) {
            log.warn("端口 {} 被占用，但无法获取占用进程信息", port);
            return 0;
        }
        
        log.info("发现端口 {} 被以下进程占用: {}", port, pids);
        
        int successCount = 0;
        for (String pid : pids) {
            if (killProcess(pid)) {
                successCount++;
            }
        }
        
        // 等待一段时间让进程完全关闭
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 再次检查端口状态
        if (isPortInUse(port)) {
            log.warn("端口 {} 仍被占用，可能需要手动处理", port);
        } else {
            log.info("端口 {} 已释放", port);
        }
        
        return successCount;
    }

    /**
     * 获取进程信息
     *
     * @param pid 进程ID
     * @return 进程信息字符串
     */
    public static String getProcessInfo(String pid) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            String command;
            
            if (os.contains("win")) {
                // Windows系统
                command = "tasklist /FI \"PID eq " + pid + "\" /FO CSV";
            } else {
                // Linux/Unix系统
                command = "ps -p " + pid + " -o pid,ppid,cmd";
            }
            
            Process process = Runtime.getRuntime().exec(command);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            
            StringBuilder result = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                result.append(line).append("\n");
            }
            
            process.waitFor();
            reader.close();
            
            return result.toString().trim();
        } catch (Exception e) {
            log.error("获取进程信息失败 PID: {}, 错误: {}", pid, e.getMessage());
            return "无法获取进程信息";
        }
    }
}
