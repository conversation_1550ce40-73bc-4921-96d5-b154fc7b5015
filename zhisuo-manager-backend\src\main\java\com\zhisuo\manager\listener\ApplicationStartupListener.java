package com.zhisuo.manager.listener;

import com.zhisuo.manager.util.PortUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationEnvironmentPreparedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * 应用启动监听器
 * 在Spring Boot应用启动前检查端口占用情况并自动处理
 */
@Slf4j
@Component
public class ApplicationStartupListener implements ApplicationListener<ApplicationEnvironmentPreparedEvent> {

    @Override
    public void onApplicationEvent(ApplicationEnvironmentPreparedEvent event) {
        Environment environment = event.getEnvironment();
        
        // 获取配置的端口号
        String portStr = environment.getProperty("server.port");
        if (portStr == null) {
            portStr = "8081"; // Spring Boot默认端口
        }
        
        try {
            int port = Integer.parseInt(portStr);
            log.info("=== 应用启动端口检查 ===");
            log.info("检查端口: {}", port);
            
            // 检查端口是否被占用
            if (PortUtils.isPortInUse(port)) {
                log.warn("端口 {} 已被占用，正在尝试释放...", port);
                
                // 获取占用进程信息
                java.util.List<String> pids = PortUtils.getProcessIdsByPort(port);
                if (!pids.isEmpty()) {
                    log.info("占用端口 {} 的进程PID: {}", port, pids);
                    
                    // 显示进程详细信息
                    for (String pid : pids) {
                        String processInfo = PortUtils.getProcessInfo(pid);
                        log.info("进程 {} 信息:\n{}", pid, processInfo);
                    }
                    
                    // 询问用户是否要关闭占用进程
                    boolean shouldKill = shouldKillOccupyingProcesses(port, pids, environment);
                    
                    if (shouldKill) {
                        // 关闭占用进程
                        int killedCount = PortUtils.killProcessesByPort(port);
                        if (killedCount > 0) {
                            log.info("成功关闭 {} 个占用端口 {} 的进程", killedCount, port);
                        } else {
                            log.error("无法关闭占用端口 {} 的进程，应用可能无法正常启动", port);
                            // 可以选择退出应用或继续尝试启动
                            handlePortOccupiedFailure(port, environment);
                        }
                    } else {
                        log.warn("用户选择不关闭占用进程，应用可能无法正常启动");
                        handlePortOccupiedFailure(port, environment);
                    }
                } else {
                    log.error("端口 {} 被占用但无法获取占用进程信息", port);
                    handlePortOccupiedFailure(port, environment);
                }
            } else {
                log.info("端口 {} 可用，应用可以正常启动", port);
            }
            
            log.info("=== 端口检查完成 ===");
            
        } catch (NumberFormatException e) {
            log.error("无效的端口配置: {}", portStr);
        } catch (Exception e) {
            log.error("端口检查过程中发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 判断是否应该关闭占用进程
     * 可以根据需要修改这个逻辑，比如添加配置项控制
     *
     * @param port 端口号
     * @param pids 占用进程PID列表
     * @param environment Spring环境对象
     * @return true-应该关闭，false-不关闭
     */
    private boolean shouldKillOccupyingProcesses(int port, java.util.List<String> pids, Environment environment) {
        // 检查配置文件中的设置
        Boolean autoKill = environment.getProperty("app.port-check.auto-kill-processes", Boolean.class);
        if (autoKill != null && !autoKill) {
            log.info("配置禁用自动关闭占用进程功能");
            return false;
        }

        // 检查系统属性（优先级更高）
        String autoKillProperty = System.getProperty("app.auto-kill-port-processes");
        if ("false".equalsIgnoreCase(autoKillProperty)) {
            log.info("系统属性禁用自动关闭占用进程功能");
            return false;
        }

        // 检查是否是开发环境（可以更激进地关闭进程）
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if ("dev".equals(profile) || "development".equals(profile)) {
                log.info("开发环境，自动关闭占用进程");
                return true;
            }

            // 生产环境更谨慎
            if ("prod".equals(profile) || "production".equals(profile)) {
                log.warn("生产环境检测到端口占用，建议手动处理");
                return false;
            }
        }

        // 默认情况下根据配置决定（如果没有配置则自动关闭）
        boolean defaultAutoKill = autoKill != null ? autoKill : true;
        log.info("默认策略：{}", defaultAutoKill ? "自动关闭占用进程" : "不自动关闭占用进程");
        return defaultAutoKill;
    }

    /**
     * 处理端口占用无法解决的情况
     *
     * @param port 端口号
     * @param environment Spring环境对象
     */
    private void handlePortOccupiedFailure(int port, Environment environment) {
        String message = String.format(
            "端口 %d 被占用且无法自动释放。\n" +
            "请手动处理：\n" +
            "1. 使用命令查看占用进程: netstat -ano | findstr :%d\n" +
            "2. 关闭占用进程: taskkill /PID <PID> /F\n" +
            "3. 或者修改应用端口配置",
            port, port
        );

        log.error(message);

        // 检查配置文件中的设置
        Boolean exitOnOccupied = environment.getProperty("app.port-check.exit-on-occupied", Boolean.class);

        // 检查系统属性（优先级更高）
        String exitOnPortOccupied = System.getProperty("app.exit-on-port-occupied");

        boolean shouldExit = false;
        if (exitOnPortOccupied != null) {
            shouldExit = "true".equalsIgnoreCase(exitOnPortOccupied);
        } else if (exitOnOccupied != null) {
            shouldExit = exitOnOccupied;
        }

        if (shouldExit) {
            log.error("配置要求端口占用时退出应用");
            System.exit(1);
        } else {
            log.warn("继续启动应用，但可能会因端口占用而失败");
        }
    }
}
