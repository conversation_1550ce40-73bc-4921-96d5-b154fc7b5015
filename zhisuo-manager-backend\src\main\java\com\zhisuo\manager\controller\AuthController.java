package com.zhisuo.manager.controller;

import cn.hutool.core.util.StrUtil;
import com.zhisuo.manager.common.Result;
import com.zhisuo.manager.dto.LoginRequest;
import com.zhisuo.manager.entity.Admin;
import com.zhisuo.manager.service.AuthService;
import com.zhisuo.manager.vo.LoginResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 认证控制器
 */
@Slf4j
@RestController
@RequestMapping("/auth")
public class AuthController {
    
    @Autowired
    private AuthService authService;
    
    @Value("${jwt.header}")
    private String tokenHeader;
    
    @Value("${jwt.prefix}")
    private String tokenPrefix;
    
    /**
     * 管理员登录
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request, HttpServletRequest httpRequest) {
        try {
            LoginResponse response = authService.login(request);
            
            // 更新最后登录信息
            String clientIp = getClientIp(httpRequest);
            authService.updateLastLogin(response.getAdminInfo().getAdminId(), clientIp);
            
            return Result.success("登录成功", response);
        } catch (Exception e) {
            log.error("登录失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 管理员登出
     */
    @PostMapping("/logout")
    public Result<Void> logout(HttpServletRequest request) {
        try {
            String authHeader = request.getHeader(tokenHeader);
            if (StrUtil.isNotBlank(authHeader) && authHeader.startsWith(tokenPrefix)) {
                String token = authHeader.substring(tokenPrefix.length()).trim();
                authService.logout(token);
            }
            return Result.success("登出成功", null);
        } catch (Exception e) {
            log.error("登出失败", e);
            return Result.error("登出失败");
        }
    }
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/current")
    public Result<LoginResponse.AdminInfo> getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof Admin) {
                Admin admin = (Admin) authentication.getPrincipal();
                
                LoginResponse.AdminInfo adminInfo = new LoginResponse.AdminInfo();
                adminInfo.setAdminId(admin.getAdminId());
                adminInfo.setUsername(admin.getUsername());
                adminInfo.setRealName(admin.getRealName());
                adminInfo.setEmail(admin.getEmail());
                adminInfo.setAvatar(admin.getAvatar());
                adminInfo.setRole(admin.getRole());
                
                return Result.success(adminInfo);
            }
            return Result.unauthorized();
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return Result.error("获取用户信息失败");
        }
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
