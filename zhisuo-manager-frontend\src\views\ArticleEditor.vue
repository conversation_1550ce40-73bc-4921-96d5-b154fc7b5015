<template>
  <div class="article-editor-page">
    <div class="page-header">
      <div class="header-left">
        <h2>{{ isEdit ? '编辑文章' : '发布文章' }}</h2>
        <p>{{ isEdit ? '修改文章内容' : '创建新的文章内容' }}</p>
      </div>
      <div class="header-right">
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleSaveDraft">保存草稿</el-button>
        <el-button type="primary" @click="handlePublish">
          {{ isEdit ? '更新文章' : '发布文章' }}
        </el-button>
      </div>
    </div>

    <div class="editor-container">
      <div class="editor-main">
        <div class="content-card">
          <el-form :model="articleForm" :rules="formRules" ref="formRef" label-width="80px">
            <el-form-item label="文章标题" prop="title">
              <el-input
                v-model="articleForm.title"
                placeholder="请输入文章标题"
                maxlength="100"
                show-word-limit
                size="large"
              />
            </el-form-item>

            <el-form-item label="文章摘要" prop="summary">
              <el-input
                v-model="articleForm.summary"
                type="textarea"
                placeholder="请输入文章摘要"
                :rows="3"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="文章内容" prop="content">
              <div class="editor-wrapper">
                <el-input
                  v-model="articleForm.content"
                  type="textarea"
                  placeholder="请输入文章内容..."
                  :rows="15"
                  class="content-editor"
                />
              </div>
            </el-form-item>

            <el-form-item label="文章标签" prop="tags">
              <el-select
                v-model="articleForm.tags"
                multiple
                filterable
                allow-create
                placeholder="请选择或输入标签"
                style="width: 100%"
              >
                <el-option
                  v-for="tag in availableTags"
                  :key="tag.value"
                  :label="tag.label"
                  :value="tag.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div class="editor-sidebar">
        <div class="content-card">
          <h3 class="sidebar-title">发布设置</h3>
          
          <div class="setting-item">
            <label class="setting-label">文章状态</label>
            <el-radio-group v-model="articleForm.status">
              <el-radio :label="0">草稿</el-radio>
              <el-radio :label="1">发布</el-radio>
            </el-radio-group>
          </div>

          <div class="setting-item">
            <label class="setting-label">文章分类</label>
            <el-select v-model="articleForm.categoryId" placeholder="请选择分类" style="width: 100%">
              <el-option
                v-for="category in categories"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              />
            </el-select>
          </div>

          <div class="setting-item">
            <label class="setting-label">封面图片</label>
            <div class="cover-upload">
              <el-upload
                class="cover-uploader"
                action="#"
                :show-file-list="false"
                :before-upload="beforeCoverUpload"
                :http-request="handleCoverUpload"
              >
                <img v-if="articleForm.coverImage" :src="articleForm.coverImage" class="cover-image" />
                <el-icon v-else class="cover-uploader-icon"><Plus /></el-icon>
              </el-upload>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label">发布时间</label>
            <el-date-picker
              v-model="articleForm.publishTime"
              type="datetime"
              placeholder="选择发布时间"
              style="width: 100%"
            />
          </div>
        </div>

        <div class="content-card">
          <h3 class="sidebar-title">SEO设置</h3>
          
          <div class="setting-item">
            <label class="setting-label">SEO标题</label>
            <el-input
              v-model="articleForm.seoTitle"
              placeholder="SEO标题"
              maxlength="60"
              show-word-limit
            />
          </div>

          <div class="setting-item">
            <label class="setting-label">SEO描述</label>
            <el-input
              v-model="articleForm.seoDescription"
              type="textarea"
              placeholder="SEO描述"
              :rows="3"
              maxlength="160"
              show-word-limit
            />
          </div>

          <div class="setting-item">
            <label class="setting-label">SEO关键词</label>
            <el-input
              v-model="articleForm.seoKeywords"
              placeholder="用逗号分隔关键词"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const isEdit = ref(false)
const formRef = ref()

// 表单数据
const articleForm = reactive({
  title: '',
  summary: '',
  content: '',
  tags: [],
  status: 0,
  categoryId: '',
  coverImage: '',
  publishTime: null,
  seoTitle: '',
  seoDescription: '',
  seoKeywords: ''
})

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入文章标题', trigger: 'blur' },
    { min: 5, max: 100, message: '标题长度在 5 到 100 个字符', trigger: 'blur' }
  ],
  summary: [
    { required: true, message: '请输入文章摘要', trigger: 'blur' },
    { min: 10, max: 200, message: '摘要长度在 10 到 200 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入文章内容', trigger: 'blur' },
    { min: 50, message: '内容至少需要 50 个字符', trigger: 'blur' }
  ]
}

// 可用标签
const availableTags = ref([
  { label: '技术', value: 'tech' },
  { label: '产品', value: 'product' },
  { label: '设计', value: 'design' },
  { label: '运营', value: 'operation' }
])

// 文章分类
const categories = ref([
  { id: 1, name: '技术分享' },
  { id: 2, name: '产品动态' },
  { id: 3, name: '行业资讯' },
  { id: 4, name: '公司新闻' }
])

// 取消编辑
const handleCancel = async () => {
  try {
    await ElMessageBox.confirm('确定要取消编辑吗？未保存的内容将丢失。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    router.back()
  } catch {
    // 用户取消
  }
}

// 保存草稿
const handleSaveDraft = async () => {
  articleForm.status = 0
  await saveArticle()
}

// 发布文章
const handlePublish = async () => {
  try {
    await formRef.value.validate()
    articleForm.status = 1
    await saveArticle()
  } catch (error) {
    ElMessage.error('请完善文章信息')
  }
}

// 保存文章
const saveArticle = async () => {
  try {
    // 这里应该调用API保存文章
    console.log('保存文章:', articleForm)
    ElMessage.success(articleForm.status === 1 ? '文章发布成功' : '草稿保存成功')
    router.push('/articles')
  } catch (error) {
    ElMessage.error('保存失败，请重试')
  }
}

// 封面上传前验证
const beforeCoverUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 处理封面上传
const handleCoverUpload = (options) => {
  // 这里应该实现实际的上传逻辑
  const file = options.file
  const reader = new FileReader()
  reader.onload = (e) => {
    articleForm.coverImage = e.target.result
  }
  reader.readAsDataURL(file)
}

onMounted(() => {
  // 如果是编辑模式，加载文章数据
  if (route.params.id) {
    isEdit.value = true
    // 加载文章数据
  }
})
</script>

<style lang="scss" scoped>
.article-editor-page {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .header-left {
      h2 {
        font-size: 24px;
        font-weight: 600;
        color: #1F2937;
        margin: 0 0 8px 0;
      }

      p {
        color: #6B7280;
        margin: 0;
        font-size: 14px;
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  .editor-container {
    flex: 1;
    display: flex;
    gap: 24px;
    overflow: hidden;

    .editor-main {
      flex: 1;
      overflow-y: auto;

      .content-card {
        height: fit-content;
      }

      .editor-wrapper {
        .content-editor {
          :deep(.el-textarea__inner) {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.6;
            border-radius: 8px;
            border: 1px solid #E5E7EB;
            
            &:focus {
              border-color: #8B5CF6;
              box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
            }
          }
        }
      }
    }

    .editor-sidebar {
      width: 320px;
      overflow-y: auto;

      .sidebar-title {
        font-size: 16px;
        font-weight: 600;
        color: #1F2937;
        margin: 0 0 20px 0;
        padding-bottom: 12px;
        border-bottom: 1px solid #E5E7EB;
      }

      .setting-item {
        margin-bottom: 20px;

        .setting-label {
          display: block;
          font-size: 14px;
          font-weight: 500;
          color: #374151;
          margin-bottom: 8px;
        }
      }

      .cover-upload {
        .cover-uploader {
          :deep(.el-upload) {
            border: 1px dashed #E5E7EB;
            border-radius: 8px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            width: 100%;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
              border-color: #8B5CF6;
            }
          }

          .cover-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 8px;
          }

          .cover-uploader-icon {
            font-size: 28px;
            color: #8C9CAF;
          }
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .article-editor-page {
    .editor-container {
      flex-direction: column;

      .editor-sidebar {
        width: 100%;
      }
    }
  }
}
</style>
