package com.zhisuo.manager.util;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.ServerSocket;
import java.util.List;

/**
 * 端口工具类测试
 */
public class PortUtilsTest {
    
    private static final Logger log = LoggerFactory.getLogger(PortUtilsTest.class);
    
    @Test
    public void testPortCheck() {
        int testPort = 8081;
        
        log.info("=== 端口工具测试 ===");
        
        // 测试端口是否被占用
        boolean inUse = PortUtils.isPortInUse(testPort);
        log.info("端口 {} 是否被占用: {}", testPort, inUse);
        
        if (inUse) {
            // 获取占用进程
            List<String> pids = PortUtils.getProcessIdsByPort(testPort);
            log.info("占用端口 {} 的进程: {}", testPort, pids);
            
            // 显示进程信息
            for (String pid : pids) {
                String processInfo = PortUtils.getProcessInfo(pid);
                log.info("进程 {} 信息:\n{}", pid, processInfo);
            }
            
            // 关闭占用进程
            if (!pids.isEmpty()) {
                int killedCount = PortUtils.killProcessesByPort(testPort);
                log.info("成功关闭 {} 个进程", killedCount);
                
                // 再次检查端口状态
                boolean stillInUse = PortUtils.isPortInUse(testPort);
                log.info("关闭进程后，端口 {} 是否仍被占用: {}", testPort, stillInUse);
            }
        } else {
            log.info("端口 {} 可用", testPort);
        }
        
        log.info("=== 测试完成 ===");
    }
    
    @Test
    public void testCreatePortOccupation() {
        int testPort = 8082;
        
        log.info("=== 创建端口占用测试 ===");
        
        try {
            // 创建一个占用端口的服务器
            ServerSocket serverSocket = new ServerSocket(testPort);
            log.info("成功创建服务器占用端口 {}", testPort);
            
            // 测试检查功能
            boolean inUse = PortUtils.isPortInUse(testPort);
            log.info("端口 {} 是否被占用: {}", testPort, inUse);
            
            if (inUse) {
                List<String> pids = PortUtils.getProcessIdsByPort(testPort);
                log.info("占用端口 {} 的进程: {}", testPort, pids);
            }
            
            // 关闭服务器
            serverSocket.close();
            log.info("关闭服务器");
            
            // 再次检查
            boolean stillInUse = PortUtils.isPortInUse(testPort);
            log.info("关闭服务器后，端口 {} 是否仍被占用: {}", testPort, stillInUse);
            
        } catch (IOException e) {
            log.error("测试失败: {}", e.getMessage());
        }
        
        log.info("=== 测试完成 ===");
    }
    
    public static void main(String[] args) {
        PortUtilsTest test = new PortUtilsTest();
        test.testPortCheck();
        test.testCreatePortOccupation();
    }
}
