package com.zhisuo.manager;

import com.zhisuo.manager.listener.ApplicationStartupListener;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 智索管理系统启动类
 */
@SpringBootApplication
@MapperScan("com.zhisuo.manager.mapper")
public class ManagerApplication {

    public static void main(String[] args) {
        // 创建SpringApplication实例
        SpringApplication application = new SpringApplication(ManagerApplication.class);

        // 添加启动监听器，用于检查端口占用
        application.addListeners(new ApplicationStartupListener());

        // 启动应用
        application.run(args);
    }

}
