<template>
  <div class="articles-page">
    <div class="page-header">
      <h2>文章管理</h2>
      <p>管理系统文章内容</p>
    </div>

    <!-- 搜索和操作区域 -->
    <div class="content-card">
      <el-form :model="queryForm" inline class="search-form">
        <el-form-item label="标题">
          <el-input
            v-model="queryForm.title"
            placeholder="请输入文章标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="作者">
          <el-input
            v-model="queryForm.author"
            placeholder="请输入作者"
            clearable
            style="width: 150px"
          />
        </el-form-item>

        <el-form-item label="状态">
          <el-select v-model="queryForm.status" placeholder="请选择" clearable style="width: 120px">
            <el-option label="已发布" :value="1" />
            <el-option label="草稿" :value="0" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 文章列表 -->
    <div class="content-card">
      <div class="table-header">
        <div class="table-title">文章列表</div>
        <div class="table-actions">
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新增文章
          </el-button>
          <el-button @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="articleList"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="articleId" label="文章ID" width="120" show-overflow-tooltip />
        <el-table-column prop="title" label="标题" width="200" show-overflow-tooltip />
        <el-table-column prop="author" label="作者" width="120" />

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'info'" size="small">
              {{ row.statusName }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="viewCount" label="阅读量" width="100" />
        <el-table-column prop="likeCount" label="点赞数" width="100" />
        <el-table-column prop="commentCount" label="评论数" width="100" />

        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button
              :type="row.status === 1 ? 'warning' : 'success'"
              size="small"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 1 ? '下架' : '发布' }}
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { articleApi } from '@/api/content'

// 响应式数据
const loading = ref(false)
const articleList = ref([])

// 查询表单
const queryForm = reactive({
  title: '',
  author: '',
  status: null
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchArticleList()
}

// 重置
const handleReset = () => {
  Object.keys(queryForm).forEach(key => {
    queryForm[key] = key === 'status' ? null : ''
  })
  pagination.current = 1
  fetchArticleList()
}

// 刷新
const handleRefresh = () => {
  fetchArticleList()
}

// 新增文章
const handleCreate = () => {
  ElMessage.info('新增文章功能开发中...')
}

// 编辑文章
const handleEdit = (article) => {
  ElMessage.info('编辑文章功能开发中...')
}

// 切换文章状态
const handleToggleStatus = async (article) => {
  const action = article.status === 1 ? '下架' : '发布'
  try {
    await ElMessageBox.confirm(
      `确定要${action}文章 "${article.title}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.info(`${action}功能开发中...`)
  } catch (error) {
    // 用户取消操作
  }
}

// 删除文章
const handleDelete = async (article) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文章 "${article.title}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.info('删除功能开发中...')
  } catch (error) {
    // 用户取消操作
  }
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchArticleList()
}

// 当前页改变
const handleCurrentChange = (current) => {
  pagination.current = current
  fetchArticleList()
}

// 获取文章列表
const fetchArticleList = async () => {
  loading.value = true
  try {
    // 模拟数据
    const mockData = {
      records: [
        {
          articleId: 'article001',
          title: '智索应用技术架构解析',
          author: '技术团队',
          status: 1,
          statusName: '已发布',
          viewCount: 1256,
          likeCount: 89,
          commentCount: 23,
          createTime: '2024-01-15T10:30:00'
        },
        {
          articleId: 'article002',
          title: '用户体验优化实践',
          author: '产品团队',
          status: 0,
          statusName: '草稿',
          viewCount: 0,
          likeCount: 0,
          commentCount: 0,
          createTime: '2024-01-16T09:20:00'
        }
      ],
      total: 2
    }

    articleList.value = mockData.records
    pagination.total = mockData.total
  } catch (error) {
    ElMessage.error('获取文章列表失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchArticleList()
})
</script>

<style lang="scss" scoped>
.articles-page {
  padding: 24px;

  .page-header {
    margin-bottom: 24px;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #1F2937;
      margin: 0 0 8px 0;
    }

    p {
      color: #6B7280;
      margin: 0;
      font-size: 14px;
    }
  }

  .search-form {
    .el-form-item {
      margin-bottom: 16px;

      :deep(.el-form-item__label) {
        font-weight: 500;
        color: #374151;
      }
    }
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .table-title {
      font-size: 18px;
      font-weight: 600;
      color: #1F2937;
    }

    .table-actions {
      display: flex;
      gap: 12px;
    }
  }

  :deep(.el-table) {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #E5E7EB;

    .el-table__header {
      background-color: #F9FAFB;
    }

    .el-table__row {
      &:hover {
        background-color: #F9FAFB;
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 24px;
    padding: 16px 0;
  }

  .status-tag {
    &.published {
      background-color: #D1FAE5;
      color: #065F46;
      border: 1px solid #A7F3D0;
    }

    &.draft {
      background-color: #FEF3C7;
      color: #92400E;
      border: 1px solid #FDE68A;
    }
  }

  .action-buttons {
    display: flex;
    gap: 8px;
  }
}
</style>
