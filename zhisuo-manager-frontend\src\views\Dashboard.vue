<template>
  <div class="dashboard">
    <div class="page-header">
      <div class="header-left">
        <h2>数据概览</h2>
        <p>欢迎回来，以下是今日数据概览</p>
      </div>
      <div class="header-right">
        <span class="date-info">今日：2023年7月18日</span>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="24" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card">
          <div class="stat-content">
            <div class="stat-title">总用户数</div>
            <div class="stat-value">2</div>
            <div class="stat-trend">
              <span class="trend-text">同比增长</span>
              <span class="trend-value positive">+5.2%</span>
            </div>
          </div>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card">
          <div class="stat-content">
            <div class="stat-title">文章总数</div>
            <div class="stat-value">2,345</div>
            <div class="stat-trend">
              <span class="trend-text">同比增长</span>
              <span class="trend-value positive">+8.6%</span>
            </div>
          </div>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card">
          <div class="stat-content">
            <div class="stat-title">热点话题</div>
            <div class="stat-value">456</div>
            <div class="stat-trend">
              <span class="trend-text">同比增长</span>
              <span class="trend-value positive">+12.3%</span>
            </div>
          </div>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card">
          <div class="stat-content">
            <div class="stat-title">总访问量</div>
            <div class="stat-value">12.3w</div>
            <div class="stat-trend">
              <span class="trend-text">同比增长</span>
              <span class="trend-value positive">+15.1%</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="24" class="charts-row">
      <el-col :xs="24" :lg="16">
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户增长趋势 (30天)</h3>
            <div class="chart-legend">
              <span class="legend-item">
                <span class="legend-color" style="background: #8B5CF6;"></span>
                新增用户
              </span>
            </div>
          </div>
          <div class="chart-container" ref="userChartRef"></div>
        </div>
      </el-col>

      <el-col :xs="24" :lg="8">
        <div class="chart-card">
          <div class="chart-header">
            <h3>内容分布</h3>
          </div>
          <div class="chart-container pie-chart" ref="contentChartRef"></div>
        </div>
      </el-col>
    </el-row>


  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { statisticsApi } from '@/api/statistics'

const userChartRef = ref()
const contentChartRef = ref()
const loading = ref(false)

// 统计数据
const stats = ref({
  totalUsers: 12846,
  todayNewUsers: 56,
  totalArticles: 3254,
  todayArticles: 23,
  totalTopics: 298,
  todayTopics: 12,
  totalViews: 1257543,
  todayViews: 3456
})





// 获取统计数据
const fetchStatistics = async () => {
  loading.value = true
  try {
    const response = await statisticsApi.getOverallStatistics()
    if (response.code === 200) {
      stats.value = response.data
    } else {
      console.error('获取统计数据失败:', response.message)
      // 使用默认数据
      stats.value = {
        totalUsers: 1234,
        todayNewUsers: 56,
        totalArticles: 2345,
        todayArticles: 23,
        totalTopics: 456,
        todayTopics: 12,
        totalViews: 123456,
        todayViews: 3456
      }
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 使用默认数据
    stats.value = {
      totalUsers: 1234,
      todayNewUsers: 56,
      totalArticles: 2345,
      todayArticles: 23,
      totalTopics: 456,
      todayTopics: 12,
      totalViews: 123456,
      todayViews: 3456
    }
  } finally {
    loading.value = false
  }
}

// 初始化用户增长图表
const initUserChart = () => {
  const chart = echarts.init(userChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#E5E7EB',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      axisLine: {
        lineStyle: {
          color: '#E5E7EB'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#6B7280'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#6B7280'
      },
      splitLine: {
        lineStyle: {
          color: '#F3F4F6'
        }
      }
    },
    series: [{
      data: [3200, 2800, 3600, 4200, 5000, 5400, 5800, 6200, 6000, 6400, 6800, 7200],
      type: 'line',
      smooth: true,
      lineStyle: {
        color: '#8B5CF6',
        width: 3
      },
      itemStyle: {
        color: '#8B5CF6',
        borderWidth: 2,
        borderColor: '#FFFFFF'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: 'rgba(139, 92, 246, 0.3)'
          }, {
            offset: 1, color: 'rgba(139, 92, 246, 0.05)'
          }]
        }
      },
      emphasis: {
        itemStyle: {
          color: '#7C3AED',
          borderColor: '#FFFFFF',
          borderWidth: 3,
          shadowBlur: 10,
          shadowColor: 'rgba(139, 92, 246, 0.5)'
        }
      }
    }]
  }
  chart.setOption(option)
  
  // 响应式
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 初始化内容分布图表
const initContentChart = () => {
  const chart = echarts.init(contentChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#E5E7EB',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      },
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      left: 'center',
      textStyle: {
        color: '#6B7280',
        fontSize: 12
      },
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 16
    },
    series: [{
      name: '内容分布',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '45%'],
      data: [
        { value: 35, name: '科技', itemStyle: { color: '#8B5CF6' } },
        { value: 25, name: '财经', itemStyle: { color: '#10B981' } },
        { value: 20, name: '社会', itemStyle: { color: '#3B82F6' } },
        { value: 10, name: '娱乐', itemStyle: { color: '#F59E0B' } },
        { value: 10, name: '体育', itemStyle: { color: '#EF4444' } }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        show: true,
        position: 'inside',
        formatter: '{d}%',
        fontSize: 12,
        color: '#FFFFFF',
        fontWeight: 'bold'
      },
      labelLine: {
        show: false
      }
    }]
  }
  chart.setOption(option)

  // 响应式
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

onMounted(async () => {
  // 获取统计数据
  await fetchStatistics()

  await nextTick()
  initUserChart()
  initContentChart()
})
</script>

<style lang="scss" scoped>
.dashboard {
  background: #F8FAFC;
  min-height: 100vh;
  padding: 24px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;

    .header-left {
      h2 {
        font-size: 28px;
        font-weight: 700;
        color: #1F2937;
        margin: 0 0 8px 0;
      }

      p {
        color: #6B7280;
        margin: 0;
        font-size: 16px;
      }
    }

    .header-right {
      .date-info {
        color: #6B7280;
        font-size: 14px;
        background: #FFFFFF;
        padding: 8px 16px;
        border-radius: 8px;
        border: 1px solid #E5E7EB;
      }
    }
  }

  .stats-row {
    margin-bottom: 32px;
  }

  .stat-card {
    background: #FFFFFF;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #F3F4F6;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .stat-content {
      .stat-title {
        font-size: 14px;
        color: #6B7280;
        margin-bottom: 12px;
        font-weight: 500;
      }

      .stat-value {
        font-size: 36px;
        font-weight: 700;
        color: #1F2937;
        margin-bottom: 12px;
        line-height: 1;
      }

      .stat-trend {
        display: flex;
        align-items: center;
        gap: 8px;

        .trend-text {
          font-size: 12px;
          color: #9CA3AF;
        }

        .trend-value {
          font-size: 12px;
          font-weight: 600;

          &.positive {
            color: #10B981;
          }

          &.negative {
            color: #EF4444;
          }
        }
      }
    }
  }

  .charts-row {
    margin-bottom: 32px;
  }

  .chart-card {
    background: #FFFFFF;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #F3F4F6;
    height: 400px;

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      h3 {
        font-size: 18px;
        font-weight: 600;
        color: #1F2937;
        margin: 0;
      }

      .chart-legend {
        display: flex;
        gap: 16px;

        .legend-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          color: #6B7280;

          .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
          }
        }
      }
    }

    .chart-container {
      height: calc(100% - 60px);

      &.pie-chart {
        height: calc(100% - 60px);
        min-height: 300px;
      }
    }
  }


}

@media (max-width: 768px) {
  .dashboard {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-left h2 {
        font-size: 24px;
      }
    }

    .stat-card {
      padding: 20px;

      .stat-content .stat-value {
        font-size: 28px;
      }

      .stat-icon {
        width: 40px;
        height: 40px;
      }
    }

    .chart-card {
      padding: 20px;
      height: 350px;

      .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .chart-legend {
          flex-wrap: wrap;
        }
      }

      .chart-container {
        &.pie-chart {
          height: calc(100% - 80px);
          min-height: 250px;
        }
      }
    }


  }
}
</style>
